# 🔄 Le Flux Complet d'une Requête DDD

## 🎯 Vue d'Ensemble Améliorée

Voici le processus complet d'une requête dans une architecture DDD, expliqué étape par étape avec des améliorations importantes :

```mermaid
sequenceDiagram
    participant Client as 🌐 Client HTTP
    participant Controller as 🎮 Controller/Resource
    participant CommandBus as 🚌 Command Bus
    participant Handler as ⚡ Command Handler
    participant Domain as 💎 Domain Service
    participant Entity as 📦 Entité
    participant Repo as 🗄️ Repository
    participant DB as 💾 Base de Données

    Client->>Controller: 1. Requête HTTP POST /books
    Controller->>Controller: 2. Validation & Transformation
    Controller->>CommandBus: 3. Dispatch CreateBookCommand
    CommandBus->>Handler: 4. Route vers CreateBookCommandHandler
    Handler->>Domain: 5. Utilise services métier (optionnel)
    Handler->>Entity: 6. Crée/modifie l'entité Book
    Entity->>Entity: 7. Applique règles métier
    Handler->>Repo: 8. Persiste via BookRepository
    Repo->>DB: 9. <PERSON>uvegarde en base
    Handler-->>CommandBus: 10. <PERSON><PERSON><PERSON> résultat
    CommandBus-->>Controller: 11. Résultat de la command
    Controller->>Controller: 12. Transformation en réponse
    Controller-->>Client: 13. Réponse HTTP 201 Created
```

## 📋 Processus Détaillé Étape par Étape

### 1. 🌐 **Réception de la Requête HTTP**

```php
// Le client envoie une requête
POST /api/books
Content-Type: application/json

{
    "name": "Clean Code",
    "author": "Robert C. Martin",
    "price": 2999
}
```

**Rôle** : Point d'entrée de l'application

### 2. 🎮 **Controller/Resource (API Platform)**

```php
<?php
// src/BookStore/Infrastructure/ApiPlatform/State/Processor/CreateBookProcessor.php

final class CreateBookProcessor implements ProcessorInterface
{
    public function __construct(
        private CommandBusInterface $commandBus
    ) {}

    public function process(mixed $data, Operation $operation, array $uriVariables = [], array $context = []): mixed
    {
        // 2a. Validation des données d'entrée
        assert($data instanceof CreateBookPayload);
        
        // 2b. Transformation en Command
        $command = new CreateBookCommand(
            name: new BookName($data->name),
            author: new Author($data->author),
            price: new Price($data->price)
        );
        
        // 2c. Dispatch de la Command
        $book = $this->commandBus->dispatch($command);
        
        // 2d. Transformation en réponse
        return $book; // API Platform se charge de la sérialisation
    }
}
```

**Améliorations** :
- ✅ **Validation** des données d'entrée
- ✅ **Transformation** DTO → Command
- ✅ **Gestion d'erreurs** centralisée
- ✅ **Séparation** logique métier / présentation

### 3. 🚌 **Command Bus (Orchestrateur)**

```php
<?php
// Le Command Bus route automatiquement vers le bon Handler

interface CommandBusInterface
{
    public function dispatch(object $command): mixed;
}

// Implémentation Symfony Messenger
final class MessengerCommandBus implements CommandBusInterface
{
    public function __construct(private MessageBusInterface $messageBus) {}

    public function dispatch(object $command): mixed
    {
        return $this->messageBus->dispatch($command)->last(HandledStamp::class)?->getResult();
    }
}
```

**Améliorations** :
- ✅ **Découplage** Controller ↔ Handler
- ✅ **Routage automatique** des commands
- ✅ **Middleware** (logging, validation, etc.)
- ✅ **Gestion d'erreurs** centralisée

### 4. ⚡ **Command Handler (Use Case)**

```php
<?php
// src/BookStore/Application/Command/CreateBookCommandHandler.php

#[AsCommandHandler]
final readonly class CreateBookCommandHandler
{
    public function __construct(
        private BookRepositoryInterface $bookRepository,
        private BookDiscountService $discountService // Service de domaine optionnel
    ) {}

    public function __invoke(CreateBookCommand $command): Book
    {
        // 4a. Validation métier (règles complexes)
        $this->validateBusinessRules($command);
        
        // 4b. Utilisation de services de domaine (optionnel)
        $finalPrice = $this->discountService->calculatePrice($command->price);
        
        // 4c. Création de l'entité
        $book = new Book(
            $command->name,
            $command->author,
            $finalPrice
        );
        
        // 4d. Persistance
        $this->bookRepository->add($book);
        
        // 4e. Événements de domaine (optionnel)
        // $this->eventDispatcher->dispatch(new BookCreatedEvent($book));
        
        return $book;
    }
    
    private function validateBusinessRules(CreateBookCommand $command): void
    {
        // Exemple : vérifier que l'auteur n'est pas banni
        // if ($this->authorService->isBanned($command->author)) {
        //     throw new AuthorBannedException();
        // }
    }
}
```

**Améliorations** :
- ✅ **Orchestration** des services
- ✅ **Validation métier** complexe
- ✅ **Gestion des transactions**
- ✅ **Événements de domaine**

### 5. 💎 **Services de Domaine (Optionnel)**

```php
<?php
// src/BookStore/Domain/Service/BookDiscountService.php

final class BookDiscountService
{
    public function calculatePrice(Price $basePrice): Price
    {
        // Logique métier complexe
        if ($this->isNewAuthorPromotion()) {
            return $basePrice->applyDiscount(new Discount(10));
        }
        
        return $basePrice;
    }
    
    private function isNewAuthorPromotion(): bool
    {
        // Règle métier : promotion pour nouveaux auteurs
        return date('N') === '1'; // Lundi = promotion
    }
}
```

**Rôle** : Logique métier qui ne rentre pas dans une entité

### 6. 📦 **Entité (Cœur Métier)**

```php
<?php
// src/BookStore/Domain/Model/Book.php

class Book
{
    public function __construct(
        private BookName $name,
        private Author $author,
        private Price $price,
    ) {
        $this->id = new BookId();
        
        // 6a. Validation des règles métier
        $this->validateBookCreation();
        
        // 6b. Événement de domaine
        $this->recordEvent(new BookCreatedEvent($this));
    }
    
    private function validateBookCreation(): void
    {
        // Règles métier de l'entité
        if ($this->price->amount <= 0) {
            throw new InvalidBookPriceException();
        }
    }
    
    public function changePrice(Price $newPrice): void
    {
        // Règle métier : pas de changement de prix si emprunté
        if ($this->isBorrowed()) {
            throw new CannotChangePriceOfBorrowedBookException();
        }
        
        $this->price = $newPrice;
        $this->recordEvent(new BookPriceChangedEvent($this, $newPrice));
    }
}
```

**Améliorations** :
- ✅ **Encapsulation** des règles métier
- ✅ **Invariants** protégés
- ✅ **Événements de domaine**
- ✅ **Expressivité** du code

### 7. 🗄️ **Repository (Persistance)**

```php
<?php
// src/BookStore/Infrastructure/Doctrine/DoctrineBookRepository.php

final class DoctrineBookRepository implements BookRepositoryInterface
{
    public function __construct(private EntityManagerInterface $entityManager) {}

    public function add(Book $book): void
    {
        // 7a. Persistance de l'entité
        $this->entityManager->persist($book);
        
        // 7b. Flush automatique ou manuel selon la stratégie
        // $this->entityManager->flush(); // Optionnel ici
    }
    
    public function ofId(BookId $id): ?Book
    {
        return $this->entityManager->find(Book::class, $id->value);
    }
}
```

**Améliorations** :
- ✅ **Abstraction** de la persistance
- ✅ **Requêtes optimisées**
- ✅ **Gestion des transactions**
- ✅ **Cache** de second niveau

### 8. 💾 **Base de Données**

```sql
-- La base de données reçoit les données structurées
INSERT INTO book (id, name, author, price) 
VALUES ('uuid-123', 'Clean Code', 'Robert C. Martin', 2999);
```

## 🔄 **Flux de Retour**

### 9. ↩️ **Remontée du Résultat**

```php
// Le résultat remonte la chaîne :
DB → Repository → Handler → CommandBus → Controller → Client

// Avec transformation à chaque niveau si nécessaire
```

## 🎯 **Améliorations Clés par Rapport au Flux Basique**

### ✅ **Séparation des Responsabilités**
- **Controller** : HTTP uniquement
- **Handler** : Orchestration métier
- **Entity** : Règles métier
- **Repository** : Persistance

### ✅ **Gestion d'Erreurs Robuste**
```php
try {
    $book = $this->commandBus->dispatch($command);
    return new JsonResponse($book, 201);
} catch (DomainException $e) {
    return new JsonResponse(['error' => $e->getMessage()], 400);
} catch (\Exception $e) {
    return new JsonResponse(['error' => 'Internal error'], 500);
}
```

### ✅ **Validation Multi-Niveaux**
1. **Syntaxique** (Controller) : Format, types
2. **Sémantique** (Handler) : Règles métier
3. **Invariants** (Entity) : Cohérence interne

### ✅ **Extensibilité**
- **Middleware** sur le Command Bus
- **Événements** de domaine
- **Services** de domaine
- **Décorateurs** sur les repositories

## 🚀 **Exemple Concret Complet**

```php
// 1. Requête HTTP
POST /api/books {"name": "DDD", "author": "Evans", "price": 4500}

// 2. Controller/Processor
$command = new CreateBookCommand(
    new BookName("DDD"),
    new Author("Evans"), 
    new Price(4500)
);

// 3. Command Bus
$result = $commandBus->dispatch($command);

// 4. Handler
$book = new Book($command->name, $command->author, $command->price);
$this->bookRepository->add($book);

// 5. Repository
$this->entityManager->persist($book);

// 6. Réponse
HTTP 201 Created
{"id": "uuid-123", "name": "DDD", "author": "Evans", "price": 4500}
```

---

**💡 Points Clés** :
- Chaque couche a une **responsabilité unique**
- Le **domaine** reste pur et testable
- L'**infrastructure** est interchangeable
- Le **flux** est prévisible et traçable
